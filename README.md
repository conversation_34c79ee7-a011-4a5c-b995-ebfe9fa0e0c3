# YC Mock Server

一个灵活、强大的Mock服务器，用于API开发和测试。基于Node.js和TypeScript构建，支持动态路由、条件响应、模板变量等高级功能。

## 🚀 特性

- **动态路由匹配** - 支持路径参数和通配符
- **条件响应** - 基于请求头、查询参数、请求体等条件返回不同响应
- **模板变量** - 在响应中使用动态变量
- **延迟模拟** - 配置响应延迟范围
- **热重载** - 配置文件修改后自动重载
- **管理接口** - 内置管理API用于监控和控制
- **详细日志** - 完整的请求响应日志记录
- **TypeScript** - 完全使用TypeScript开发，类型安全

## 📦 安装

```bash
# 克隆项目
git clone <repository-url>
cd yc-mock-server

# 安装依赖
pnpm install

# 复制环境配置
cp .env.example .env
```

## 🛠️ 使用方法

### 开发模式

```bash
# 启动开发服务器（支持热重载）
pnpm dev
```

### 生产模式

```bash
# 构建项目
pnpm build

# 启动生产服务器
pnpm start
```

### 其他命令

```bash
# 类型检查
pnpm type-check

# 清理构建文件
pnpm clean
```

## 📁 项目结构

```
yc-mock-server/
├── src/                    # 源代码
│   ├── config/            # 配置管理
│   ├── middleware/        # 中间件
│   ├── routes/           # 路由定义
│   ├── services/         # 业务服务
│   ├── types/            # TypeScript类型定义
│   ├── utils/            # 工具函数
│   ├── app.ts            # Express应用配置
│   └── index.ts          # 应用入口
├── config/               # Mock配置文件
│   └── mocks/           # Mock规则定义
├── dist/                # 构建输出
├── docs/                # 文档
└── examples/            # 示例文件
```

## ⚙️ 配置

### 环境变量

在`.env`文件中配置服务器参数：

```env
# 服务器配置
PORT=3000
HOST=localhost
NODE_ENV=development

# Mock配置
MOCK_CONFIG_PATH=./config/mocks
MOCK_DELAY_MIN=0
MOCK_DELAY_MAX=1000

# CORS配置
CORS_ORIGIN=*
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_CREDENTIALS=true

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=combined

# 安全配置
HELMET_ENABLED=true
```

### Mock配置文件

在`config/mocks/`目录下创建JSON文件定义Mock规则：

```json
{
  "id": "users-api",
  "name": "Users API Mock",
  "description": "用户管理API的Mock配置",
  "enabled": true,
  "routes": [
    {
      "id": "get-users",
      "method": "GET",
      "path": "/api/users",
      "response": {
        "status": 200,
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "data": [
            {
              "id": 1,
              "name": "John Doe",
              "email": "<EMAIL>"
            }
          ]
        }
      },
      "delay": {
        "min": 100,
        "max": 500
      }
    }
  ]
}
```

## 🎯 高级功能

### 路径参数

支持动态路径参数：

```json
{
  "method": "GET",
  "path": "/api/users/:id",
  "response": {
    "status": 200,
    "body": {
      "id": "{{params.id}}",
      "name": "User {{params.id}}"
    }
  }
}
```

### 条件响应

基于请求条件返回不同响应：

```json
{
  "method": "POST",
  "path": "/api/auth/login",
  "response": {
    "status": 200,
    "body": {
      "token": "success-token"
    }
  },
  "conditions": [
    {
      "type": "body",
      "key": "username",
      "operator": "equals",
      "value": "admin"
    }
  ]
}
```

### 模板变量

在响应中使用动态变量：

- `{{params.key}}` - 路径参数
- `{{query.key}}` - 查询参数
- `{{body.key}}` - 请求体字段
- `{{timestamp}}` - 当前时间戳
- `{{random}}` - 随机数

## 🔧 管理接口

服务器提供管理接口用于监控和控制：

- `GET /_admin/health` - 健康检查
- `GET /_admin/info` - 服务器信息
- `GET /_admin/configs` - 获取Mock配置
- `POST /_admin/reload` - 重载配置
- `GET /_admin/logs` - 获取日志
- `DELETE /_admin/logs` - 清空日志

## 📊 示例

### 测试登录接口

```bash
# 成功登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "123456"}'

# 失败登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "wrong", "password": "wrong"}'
```

### 获取用户列表

```bash
curl http://localhost:3000/api/users
```

### 重载配置

```bash
curl -X POST http://localhost:3000/_admin/reload
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
