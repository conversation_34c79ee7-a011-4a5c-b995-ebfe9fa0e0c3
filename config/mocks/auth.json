{"id": "auth-api", "name": "Authentication API Mock", "description": "Mock configuration for authentication endpoints", "enabled": true, "routes": [{"id": "login-success", "method": "POST", "path": "/api/auth/login", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "user": {"id": 1, "name": "{{body.username}}", "email": "{{body.username}}@example.com", "role": "user"}, "expiresIn": 3600}, "message": "Login successful"}}, "conditions": [{"type": "body", "key": "username", "operator": "equals", "value": "<EMAIL>"}, {"type": "body", "key": "password", "operator": "equals", "value": "123456"}], "delay": {"min": 300, "max": 800}}, {"id": "login-failure", "method": "POST", "path": "/api/auth/login", "response": {"status": 401, "headers": {"Content-Type": "application/json"}, "body": {"error": {"message": "Invalid username or password", "code": "INVALID_CREDENTIALS"}}}, "delay": {"min": 500, "max": 1000}}, {"id": "logout", "method": "POST", "path": "/api/auth/logout", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"message": "Logout successful"}}}, {"id": "refresh-token", "method": "POST", "path": "/api/auth/refresh", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "expiresIn": 3600}}}, "conditions": [{"type": "header", "key": "authorization", "operator": "exists"}]}, {"id": "profile", "method": "GET", "path": "/api/auth/profile", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"data": {"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "role": "user", "lastLogin": "{{timestamp}}"}}}, "conditions": [{"type": "header", "key": "authorization", "operator": "exists"}]}]}