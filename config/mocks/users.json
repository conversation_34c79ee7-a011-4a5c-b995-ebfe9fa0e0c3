{"id": "users-api", "name": "Users API Mock", "description": "Mock configuration for user management API", "enabled": true, "routes": [{"id": "get-users", "method": "GET", "path": "/api/users", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"data": [{"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "role": "admin", "createdAt": "2024-01-01T00:00:00Z"}, {"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "role": "user", "createdAt": "2024-01-02T00:00:00Z"}], "total": 2, "page": 1, "limit": 10}}, "delay": {"min": 100, "max": 500}}, {"id": "get-user-by-id", "method": "GET", "path": "/api/users/:id", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"data": {"id": "{{params.id}}", "name": "User {{params.id}}", "email": "user{{params.id}}@example.com", "role": "user", "createdAt": "{{timestamp}}"}}}, "delay": {"min": 50, "max": 200}}, {"id": "create-user", "method": "POST", "path": "/api/users", "response": {"status": 201, "headers": {"Content-Type": "application/json"}, "body": {"data": {"id": "{{random}}", "name": "{{body.name}}", "email": "{{body.email}}", "role": "user", "createdAt": "{{timestamp}}"}, "message": "User created successfully"}}, "delay": {"min": 200, "max": 800}}, {"id": "update-user", "method": "PUT", "path": "/api/users/:id", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"data": {"id": "{{params.id}}", "name": "{{body.name}}", "email": "{{body.email}}", "role": "{{body.role}}", "updatedAt": "{{timestamp}}"}, "message": "User updated successfully"}}}, {"id": "delete-user", "method": "DELETE", "path": "/api/users/:id", "response": {"status": 204}, "delay": {"min": 100, "max": 300}}]}