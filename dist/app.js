"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApp = createApp;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const MockService_1 = require("./services/MockService");
const mockHandler_1 = require("./middleware/mockHandler");
const logger_1 = require("./middleware/logger");
const errorHandler_1 = require("./middleware/errorHandler");
const admin_1 = require("./routes/admin");
const config_1 = __importDefault(require("./config"));
function createApp() {
    const app = (0, express_1.default)();
    const mockService = new MockService_1.MockService();
    const mockHandler = new mockHandler_1.MockHandler(mockService);
    if (config_1.default.server.security.helmetEnabled) {
        app.use((0, helmet_1.default)());
    }
    app.use((0, cors_1.default)({
        origin: config_1.default.server.cors.origin,
        methods: config_1.default.server.cors.methods,
        credentials: config_1.default.server.cors.credentials,
    }));
    app.use(express_1.default.json({ limit: '10mb' }));
    app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
    app.use(logger_1.requestTimer);
    app.use((0, logger_1.mockLogger)(config_1.default.server.logging.format));
    app.use('/_admin', (0, admin_1.createAdminRoutes)(mockHandler));
    app.use(mockHandler.handle);
    app.use(errorHandler_1.notFoundHandler);
    app.use(errorHandler_1.errorHandler);
    return app;
}
exports.default = createApp;
//# sourceMappingURL=app.js.map