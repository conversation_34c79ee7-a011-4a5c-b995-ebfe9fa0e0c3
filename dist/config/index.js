"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockConfig = exports.serverConfig = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
exports.serverConfig = {
    port: parseInt(process.env.PORT || '3000', 10),
    host: process.env.HOST || 'localhost',
    cors: {
        origin: process.env.CORS_ORIGIN === '*' ? '*' : process.env.CORS_ORIGIN?.split(',') || ['*'],
        methods: process.env.CORS_METHODS || 'GET,HEAD,PUT,PATCH,POST,DELETE',
        credentials: process.env.CORS_CREDENTIALS === 'true',
    },
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'combined',
    },
    security: {
        helmetEnabled: process.env.HELMET_ENABLED === 'true',
    },
};
exports.mockConfig = {
    configPath: process.env.MOCK_CONFIG_PATH || './config/mocks',
    delayMin: parseInt(process.env.MOCK_DELAY_MIN || '0', 10),
    delayMax: parseInt(process.env.MOCK_DELAY_MAX || '1000', 10),
};
exports.default = {
    server: exports.serverConfig,
    mock: exports.mockConfig,
};
//# sourceMappingURL=index.js.map