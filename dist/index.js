"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("./app");
const config_1 = __importDefault(require("./config"));
function startServer() {
    const app = (0, app_1.createApp)();
    const { port, host } = config_1.default.server;
    const server = app.listen(port, host, () => {
        console.log(`🚀 YC Mock Server is running on http://${host}:${port}`);
        console.log(`📊 Admin panel available at http://${host}:${port}/_admin`);
        console.log(`📁 Mock configs path: ${config_1.default.mock.configPath}`);
        console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    });
    process.on('SIGTERM', () => {
        console.log('SIGTERM received, shutting down gracefully');
        server.close(() => {
            console.log('Server closed');
            process.exit(0);
        });
    });
    process.on('SIGINT', () => {
        console.log('SIGINT received, shutting down gracefully');
        server.close(() => {
            console.log('Server closed');
            process.exit(0);
        });
    });
    process.on('uncaughtException', (error) => {
        console.error('Uncaught Exception:', error);
        process.exit(1);
    });
    process.on('unhandledRejection', (reason, promise) => {
        console.error('Unhandled Rejection at:', promise, 'reason:', reason);
        process.exit(1);
    });
}
startServer();
//# sourceMappingURL=index.js.map