import { Request, Response, NextFunction } from 'express';
export interface ApiError extends Error {
    status?: number;
    code?: string;
}
export declare function createError(message: string, status?: number, code?: string): ApiError;
export declare function notFoundHandler(req: Request, res: Response, next: NextFunction): void;
export declare function errorHandler(error: ApiError, req: Request, res: Response, next: NextFunction): void;
export declare function asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<any>): (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=errorHandler.d.ts.map