"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createError = createError;
exports.notFoundHandler = notFoundHandler;
exports.errorHandler = errorHandler;
exports.asyncHandler = asyncHandler;
function createError(message, status = 500, code) {
    const error = new Error(message);
    error.status = status;
    error.code = code;
    return error;
}
function notFoundHandler(req, res, next) {
    const error = createError(`Route ${req.method} ${req.path} not found`, 404, 'ROUTE_NOT_FOUND');
    next(error);
}
function errorHandler(error, req, res, next) {
    const status = error.status || 500;
    const message = error.message || 'Internal Server Error';
    const code = error.code || 'INTERNAL_ERROR';
    console.error(`Error ${status}: ${message}`, {
        method: req.method,
        path: req.path,
        stack: error.stack,
        code,
    });
    res.status(status).json({
        error: {
            message,
            code,
            status,
            timestamp: new Date().toISOString(),
            path: req.path,
            method: req.method,
        },
    });
}
function asyncHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}
//# sourceMappingURL=errorHandler.js.map