import { Request, Response, NextFunction } from 'express';
import { LogEntry } from '../types';
export declare function requestTimer(req: Request, _res: Response, next: NextFunction): void;
export declare function mockLogger(format?: string): (req: import("http").IncomingMessage, res: import("http").ServerResponse<import("http").IncomingMessage>, callback: (err?: Error) => void) => void;
export declare class Logger {
    private logs;
    private maxLogs;
    log(entry: LogEntry): void;
    getLogs(limit?: number): LogEntry[];
    clearLogs(): void;
}
export declare const logger: Logger;
//# sourceMappingURL=logger.d.ts.map