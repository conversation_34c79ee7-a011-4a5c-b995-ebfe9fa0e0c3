"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = void 0;
exports.requestTimer = requestTimer;
exports.mockLogger = mockLogger;
const morgan_1 = __importDefault(require("morgan"));
const utils_1 = require("../utils");
morgan_1.default.token('mock-info', (req) => {
    const mockReq = req;
    if (mockReq.mockId && mockReq.routeId) {
        return `[Mock: ${mockReq.mockId}/${mockReq.routeId}]`;
    }
    return '[No Mock]';
});
morgan_1.default.token('response-time-ms', (req, _res) => {
    const mockReq = req;
    const startTime = mockReq.startTime;
    if (startTime) {
        return `${(0, utils_1.formatResponseTime)(startTime)}ms`;
    }
    return '0ms';
});
const mockFormat = ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time-ms :mock-info';
function requestTimer(req, _res, next) {
    req.startTime = process.hrtime();
    next();
}
function mockLogger(format = 'combined') {
    if (format === 'mock') {
        return (0, morgan_1.default)(mockFormat);
    }
    return (0, morgan_1.default)(format);
}
class Logger {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000;
    }
    log(entry) {
        this.logs.push(entry);
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }
        const { timestamp, method, path, status, responseTime, mockId, routeId } = entry;
        const mockInfo = mockId && routeId ? `[${mockId}/${routeId}]` : '[No Mock]';
        console.log(`${timestamp.toISOString()} ${method} ${path} ${status} ${responseTime}ms ${mockInfo}`);
    }
    getLogs(limit) {
        if (limit) {
            return this.logs.slice(-limit);
        }
        return [...this.logs];
    }
    clearLogs() {
        this.logs = [];
    }
}
exports.Logger = Logger;
exports.logger = new Logger();
//# sourceMappingURL=logger.js.map