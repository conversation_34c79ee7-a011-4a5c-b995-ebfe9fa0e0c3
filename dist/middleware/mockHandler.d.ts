import { Request, Response, NextFunction } from 'express';
import { MockService } from '../services/MockService';
import { MockRequest } from '../types';
export declare class MockHandler {
    private mockService;
    constructor(mockService: MockService);
    handle: (req: MockRequest, res: Response, next: NextFunction) => Promise<void>;
    reload: (req: Request, res: Response) => void;
    getConfigs: (req: Request, res: Response) => void;
    getLogs: (req: Request, res: Response) => void;
}
//# sourceMappingURL=mockHandler.d.ts.map