"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockHandler = void 0;
const logger_1 = require("./logger");
const utils_1 = require("../utils");
class MockHandler {
    constructor(mockService) {
        this.mockService = mockService;
        this.handle = async (req, res, next) => {
            const startTime = process.hrtime();
            try {
                const route = this.mockService.findMatchingRoute(req.method, req.path, req);
                if (!route) {
                    return next();
                }
                req.mockId = route.id;
                req.routeId = route.id;
                const mockResponse = await this.mockService.processMockResponse(route, req);
                if (mockResponse.headers) {
                    Object.entries(mockResponse.headers).forEach(([key, value]) => {
                        res.setHeader(key, value);
                    });
                }
                if (!res.getHeader('content-type')) {
                    if (typeof mockResponse.body === 'object') {
                        res.setHeader('content-type', 'application/json');
                    }
                    else if (typeof mockResponse.body === 'string') {
                        res.setHeader('content-type', 'text/plain');
                    }
                }
                res.status(mockResponse.status);
                if (mockResponse.body !== undefined) {
                    if (typeof mockResponse.body === 'object') {
                        res.json(mockResponse.body);
                    }
                    else {
                        res.send(mockResponse.body);
                    }
                }
                else {
                    res.end();
                }
                const responseTime = (0, utils_1.formatResponseTime)(startTime);
                logger_1.logger.log({
                    timestamp: new Date(),
                    method: req.method,
                    path: req.path,
                    status: mockResponse.status,
                    responseTime,
                    mockId: req.mockId,
                    routeId: req.routeId,
                });
            }
            catch (error) {
                console.error('Error in mock handler:', error);
                next(error);
            }
        };
        this.reload = (req, res) => {
            try {
                this.mockService.reload();
                res.json({
                    message: 'Mock configurations reloaded successfully',
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                res.status(500).json({
                    error: 'Failed to reload mock configurations',
                    message: error instanceof Error ? error.message : 'Unknown error',
                    timestamp: new Date().toISOString(),
                });
            }
        };
        this.getConfigs = (req, res) => {
            try {
                const configs = this.mockService.getMockConfigs();
                res.json({
                    configs,
                    count: configs.length,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                res.status(500).json({
                    error: 'Failed to get mock configurations',
                    message: error instanceof Error ? error.message : 'Unknown error',
                    timestamp: new Date().toISOString(),
                });
            }
        };
        this.getLogs = (req, res) => {
            try {
                const limit = req.query.limit ? parseInt(req.query.limit, 10) : undefined;
                const logs = logger_1.logger.getLogs(limit);
                res.json({
                    logs,
                    count: logs.length,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                res.status(500).json({
                    error: 'Failed to get logs',
                    message: error instanceof Error ? error.message : 'Unknown error',
                    timestamp: new Date().toISOString(),
                });
            }
        };
    }
}
exports.MockHandler = MockHandler;
//# sourceMappingURL=mockHandler.js.map