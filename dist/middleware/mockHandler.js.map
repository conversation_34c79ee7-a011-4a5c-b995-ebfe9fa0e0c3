{"version": 3, "file": "mockHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/mockHandler.ts"], "names": [], "mappings": ";;;AAGA,qCAAkC;AAClC,oCAA8C;AAE9C,MAAa,WAAW;IACtB,YAAoB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;QAK5C,WAAM,GAAG,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;YACpF,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YAEnC,IAAI,CAAC;gBAEH,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAE5E,IAAI,CAAC,KAAK,EAAE,CAAC;oBAEX,OAAO,IAAI,EAAE,CAAC;gBAChB,CAAC;gBAGD,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC;gBACtB,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC;gBAGvB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAG5E,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBACzB,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;wBAC5D,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC;gBAGD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC;oBACnC,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC1C,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;oBACpD,CAAC;yBAAM,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACjD,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;oBAC9C,CAAC;gBACH,CAAC;gBAGD,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAEhC,IAAI,YAAY,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACpC,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC1C,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAC9B,CAAC;yBAAM,CAAC;wBACN,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAC9B,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,GAAG,CAAC,GAAG,EAAE,CAAC;gBACZ,CAAC;gBAGD,MAAM,YAAY,GAAG,IAAA,0BAAkB,EAAC,SAAS,CAAC,CAAC;gBACnD,eAAM,CAAC,GAAG,CAAC;oBACT,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,MAAM,EAAE,YAAY,CAAC,MAAM;oBAC3B,YAAY;oBACZ,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBAC/C,IAAI,CAAC,KAAK,CAAC,CAAC;YACd,CAAC;QACH,CAAC,CAAC;QAKF,WAAM,GAAG,CAAC,GAAY,EAAE,GAAa,EAAQ,EAAE;YAC7C,IAAI,CAAC;gBACH,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;gBAC1B,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,2CAA2C;oBACpD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,sCAAsC;oBAC7C,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;oBACjE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;QAKF,eAAU,GAAG,CAAC,GAAY,EAAE,GAAa,EAAQ,EAAE;YACjD,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;gBAClD,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO;oBACP,KAAK,EAAE,OAAO,CAAC,MAAM;oBACrB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,mCAAmC;oBAC1C,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;oBACjE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;QAKF,YAAO,GAAG,CAAC,GAAY,EAAE,GAAa,EAAQ,EAAE;YAC9C,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBACpF,MAAM,IAAI,GAAG,eAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnC,GAAG,CAAC,IAAI,CAAC;oBACP,IAAI;oBACJ,KAAK,EAAE,IAAI,CAAC,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,oBAAoB;oBAC3B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;oBACjE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;IAjI6C,CAAC;CAkIjD;AAnID,kCAmIC"}