"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAdminRoutes = createAdminRoutes;
const express_1 = require("express");
function createAdminRoutes(mockHandler) {
    const router = (0, express_1.Router)();
    router.get('/health', (req, res) => {
        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
        });
    });
    router.post('/reload', mockHandler.reload);
    router.get('/configs', mockHandler.getConfigs);
    router.get('/logs', mockHandler.getLogs);
    router.delete('/logs', (req, res) => {
        try {
            const { logger } = require('../middleware/logger');
            logger.clearLogs();
            res.json({
                message: 'Logs cleared successfully',
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to clear logs',
                message: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString(),
            });
        }
    });
    router.get('/info', (req, res) => {
        res.json({
            name: 'YC Mock Server',
            version: '1.0.0',
            node: process.version,
            platform: process.platform,
            arch: process.arch,
            timestamp: new Date().toISOString(),
        });
    });
    return router;
}
//# sourceMappingURL=admin.js.map