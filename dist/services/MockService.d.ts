import { MockConfig, MockRoute, MockRequest, MockResponse } from '../types';
export declare class MockService {
    private mockConfigs;
    private routes;
    constructor();
    loadMockConfigs(): void;
    private registerRoutes;
    findMatchingRoute(method: string, path: string, req: MockRequest): MockRoute | null;
    private matchesPath;
    processMockResponse(route: MockRoute, req: MockRequest): Promise<MockResponse>;
    private processTemplate;
    getMockConfigs(): MockConfig[];
    getMockConfig(id: string): MockConfig | undefined;
    reload(): void;
}
//# sourceMappingURL=MockService.d.ts.map