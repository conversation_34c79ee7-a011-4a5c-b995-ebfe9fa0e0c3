"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const utils_1 = require("../utils");
const config_1 = __importDefault(require("../config"));
class MockService {
    constructor() {
        this.mockConfigs = new Map();
        this.routes = new Map();
        this.loadMockConfigs();
    }
    loadMockConfigs() {
        console.log(`Loading mock configurations from: ${config_1.default.mock.configPath}`);
        const configFiles = (0, utils_1.getJsonFiles)(config_1.default.mock.configPath);
        this.mockConfigs.clear();
        this.routes.clear();
        configFiles.forEach(filePath => {
            const mockConfig = (0, utils_1.loadJsonFile)(filePath);
            if (mockConfig && mockConfig.enabled) {
                this.mockConfigs.set(mockConfig.id, mockConfig);
                this.registerRoutes(mockConfig);
                console.log(`Loaded mock config: ${mockConfig.name} (${mockConfig.routes.length} routes)`);
            }
        });
        console.log(`Total loaded configs: ${this.mockConfigs.size}`);
    }
    registerRoutes(config) {
        config.routes.forEach(route => {
            const key = `${route.method}:${route.path}`;
            if (!this.routes.has(key)) {
                this.routes.set(key, []);
            }
            this.routes.get(key).push(route);
        });
    }
    findMatchingRoute(method, path, req) {
        const key = `${method.toUpperCase()}:${path}`;
        const routes = this.routes.get(key);
        if (!routes) {
            for (const [routeKey, routeList] of this.routes.entries()) {
                const [routeMethod, routePath] = routeKey.split(':');
                if (routeMethod === method.toUpperCase() && this.matchesPath(routePath, path)) {
                    const matchingRoutes = routeList.filter(route => !route.conditions || (0, utils_1.matchesConditions)(req, route.conditions));
                    if (matchingRoutes.length > 0) {
                        return matchingRoutes[0];
                    }
                }
            }
            return null;
        }
        const matchingRoute = routes.find(route => !route.conditions || (0, utils_1.matchesConditions)(req, route.conditions));
        return matchingRoute || routes[0];
    }
    matchesPath(routePath, requestPath) {
        const routeSegments = routePath.split('/');
        const requestSegments = requestPath.split('/');
        if (routeSegments.length !== requestSegments.length) {
            return false;
        }
        return routeSegments.every((segment, index) => {
            if (segment.startsWith(':')) {
                return true;
            }
            return segment === requestSegments[index];
        });
    }
    async processMockResponse(route, req) {
        if (route.delay) {
            const delay = (0, utils_1.generateDelay)(route.delay);
            await (0, utils_1.applyDelay)(delay);
        }
        const response = { ...route.response };
        if (typeof response.body === 'string' && response.body.includes('{{')) {
            response.body = this.processTemplate(response.body, req);
        }
        if (response.file) {
            try {
                const filePath = path_1.default.resolve(config_1.default.mock.configPath, response.file);
                if (fs_1.default.existsSync(filePath)) {
                    const fileContent = fs_1.default.readFileSync(filePath, 'utf-8');
                    if (response.file.endsWith('.json')) {
                        response.body = JSON.parse(fileContent);
                    }
                    else {
                        response.body = fileContent;
                    }
                }
            }
            catch (error) {
                console.error(`Error loading response file ${response.file}:`, error);
            }
        }
        return response;
    }
    processTemplate(template, req) {
        return template.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
            try {
                const trimmed = expression.trim();
                if (trimmed.startsWith('query.')) {
                    const key = trimmed.substring(6);
                    return req.query[key] || '';
                }
                if (trimmed.startsWith('body.')) {
                    const key = trimmed.substring(5);
                    return req.body?.[key] || '';
                }
                if (trimmed.startsWith('params.')) {
                    const key = trimmed.substring(7);
                    return req.params[key] || '';
                }
                if (trimmed === 'timestamp') {
                    return new Date().toISOString();
                }
                if (trimmed === 'random') {
                    return Math.random().toString();
                }
                return match;
            }
            catch (error) {
                console.error(`Error processing template expression ${expression}:`, error);
                return match;
            }
        });
    }
    getMockConfigs() {
        return Array.from(this.mockConfigs.values());
    }
    getMockConfig(id) {
        return this.mockConfigs.get(id);
    }
    reload() {
        this.loadMockConfigs();
    }
}
exports.MockService = MockService;
//# sourceMappingURL=MockService.js.map