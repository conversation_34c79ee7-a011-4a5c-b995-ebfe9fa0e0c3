{"version": 3, "file": "MockService.js", "sourceRoot": "", "sources": ["../../src/services/MockService.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AAExB,oCAAoG;AACpG,uDAA+B;AAE/B,MAAa,WAAW;IAItB;QAHQ,gBAAW,GAA4B,IAAI,GAAG,EAAE,CAAC;QACjD,WAAM,GAA6B,IAAI,GAAG,EAAE,CAAC;QAGnD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAKM,eAAe;QACpB,OAAO,CAAC,GAAG,CAAC,qCAAqC,gBAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAE3E,MAAM,WAAW,GAAG,IAAA,oBAAY,EAAC,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAEpB,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC7B,MAAM,UAAU,GAAG,IAAA,oBAAY,EAAa,QAAQ,CAAC,CAAC;YACtD,IAAI,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACrC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;gBAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,uBAAuB,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,CAAC,MAAM,UAAU,CAAC,CAAC;YAC7F,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;IAChE,CAAC;IAKO,cAAc,CAAC,MAAkB;QACvC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC5B,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAC3B,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAKM,iBAAiB,CAAC,MAAc,EAAE,IAAY,EAAE,GAAgB;QACrE,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,CAAC,MAAM,EAAE,CAAC;YAEZ,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC1D,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACrD,IAAI,WAAW,KAAK,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC;oBAC9E,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC9C,CAAC,KAAK,CAAC,UAAU,IAAI,IAAA,yBAAiB,EAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,CAC9D,CAAC;oBACF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC9B,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACxC,CAAC,KAAK,CAAC,UAAU,IAAI,IAAA,yBAAiB,EAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,CAC9D,CAAC;QAEF,OAAO,aAAa,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAKO,WAAW,CAAC,SAAiB,EAAE,WAAmB;QACxD,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC3C,MAAM,eAAe,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE/C,IAAI,aAAa,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YAC5C,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,OAAO,KAAK,eAAe,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAKM,KAAK,CAAC,mBAAmB,CAAC,KAAgB,EAAE,GAAgB;QAEjE,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,KAAK,GAAG,IAAA,qBAAa,EAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QAGD,MAAM,QAAQ,GAAG,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAGvC,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACtE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC3D,CAAC;QAGD,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,gBAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACrE,IAAI,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC5B,MAAM,WAAW,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBACvD,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;wBACpC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;oBAC1C,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,IAAI,GAAG,WAAW,CAAC;oBAC9B,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,QAAQ,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,eAAe,CAAC,QAAgB,EAAE,GAAgB;QACxD,OAAO,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YAChE,IAAI,CAAC;gBAEH,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;gBAElC,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACjC,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBACjC,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBAC9B,CAAC;gBAED,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;oBAChC,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBACjC,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBAC/B,CAAC;gBAED,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClC,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBACjC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBAC/B,CAAC;gBAED,IAAI,OAAO,KAAK,WAAW,EAAE,CAAC;oBAC5B,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBAClC,CAAC;gBAED,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;oBACzB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;gBAClC,CAAC;gBAED,OAAO,KAAK,CAAC;YACf,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC5E,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAKM,cAAc;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC;IAKM,aAAa,CAAC,EAAU;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAKM,MAAM;QACX,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;CACF;AA/LD,kCA+LC"}