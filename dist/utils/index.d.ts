import { DelayConfig, Condition, MockRequest } from '../types';
export declare function generateDelay(config: DelayConfig): number;
export declare function applyDelay(delay: number): Promise<void>;
export declare function matchesConditions(req: MockRequest, conditions: Condition[]): boolean;
export declare function matchesCondition(req: MockRequest, condition: Condition): boolean;
export declare function loadJsonFile<T>(filePath: string): T | null;
export declare function saveJsonFile<T>(filePath: string, data: T): boolean;
export declare function getJsonFiles(dirPath: string): string[];
export declare function generateId(): string;
export declare function formatResponseTime(startTime: [number, number]): number;
//# sourceMappingURL=index.d.ts.map