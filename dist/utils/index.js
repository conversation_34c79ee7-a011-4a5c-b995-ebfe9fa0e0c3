"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateDelay = generateDelay;
exports.applyDelay = applyDelay;
exports.matchesConditions = matchesConditions;
exports.matchesCondition = matchesCondition;
exports.loadJsonFile = loadJsonFile;
exports.saveJsonFile = saveJsonFile;
exports.getJsonFiles = getJsonFiles;
exports.generateId = generateId;
exports.formatResponseTime = formatResponseTime;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
function generateDelay(config) {
    const { min, max } = config;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
function applyDelay(delay) {
    return new Promise(resolve => setTimeout(resolve, delay));
}
function matchesConditions(req, conditions) {
    return conditions.every(condition => matchesCondition(req, condition));
}
function matchesCondition(req, condition) {
    const { type, key, operator, value } = condition;
    let actualValue;
    switch (type) {
        case 'header':
            actualValue = req.headers[key.toLowerCase()];
            break;
        case 'query':
            actualValue = req.query[key];
            break;
        case 'body':
            actualValue = req.body?.[key];
            break;
        case 'path':
            actualValue = req.params[key];
            break;
        default:
            return false;
    }
    switch (operator) {
        case 'exists':
            return actualValue !== undefined && actualValue !== null;
        case 'equals':
            return actualValue === value;
        case 'contains':
            return typeof actualValue === 'string' && actualValue.includes(value);
        case 'regex':
            return typeof actualValue === 'string' && new RegExp(value).test(actualValue);
        default:
            return false;
    }
}
function loadJsonFile(filePath) {
    try {
        const fullPath = path_1.default.resolve(filePath);
        if (!fs_1.default.existsSync(fullPath)) {
            return null;
        }
        const content = fs_1.default.readFileSync(fullPath, 'utf-8');
        return JSON.parse(content);
    }
    catch (error) {
        console.error(`Error loading JSON file ${filePath}:`, error);
        return null;
    }
}
function saveJsonFile(filePath, data) {
    try {
        const fullPath = path_1.default.resolve(filePath);
        const dir = path_1.default.dirname(fullPath);
        if (!fs_1.default.existsSync(dir)) {
            fs_1.default.mkdirSync(dir, { recursive: true });
        }
        fs_1.default.writeFileSync(fullPath, JSON.stringify(data, null, 2), 'utf-8');
        return true;
    }
    catch (error) {
        console.error(`Error saving JSON file ${filePath}:`, error);
        return false;
    }
}
function getJsonFiles(dirPath) {
    try {
        const fullPath = path_1.default.resolve(dirPath);
        if (!fs_1.default.existsSync(fullPath)) {
            return [];
        }
        return fs_1.default.readdirSync(fullPath)
            .filter(file => file.endsWith('.json'))
            .map(file => path_1.default.join(fullPath, file));
    }
    catch (error) {
        console.error(`Error reading directory ${dirPath}:`, error);
        return [];
    }
}
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}
function formatResponseTime(startTime) {
    const diff = process.hrtime(startTime);
    return Math.round((diff[0] * 1e3) + (diff[1] * 1e-6));
}
//# sourceMappingURL=index.js.map