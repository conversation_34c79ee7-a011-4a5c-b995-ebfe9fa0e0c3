# API 文档

## Mock配置格式

### 基础结构

```typescript
interface MockConfig {
  id: string;                    // 配置唯一标识
  name: string;                  // 配置名称
  description?: string;          // 配置描述
  enabled: boolean;              // 是否启用
  routes: MockRoute[];           // 路由配置数组
}
```

### 路由配置

```typescript
interface MockRoute {
  id: string;                    // 路由唯一标识
  method: HttpMethod;            // HTTP方法
  path: string;                  // 路径（支持参数）
  response: MockResponse;        // 响应配置
  delay?: DelayConfig;           // 延迟配置
  conditions?: Condition[];      // 匹配条件
}
```

### 响应配置

```typescript
interface MockResponse {
  status: number;                // HTTP状态码
  headers?: Record<string, string>; // 响应头
  body?: any;                    // 响应体
  file?: string;                 // 响应文件路径
}
```

### 延迟配置

```typescript
interface DelayConfig {
  min: number;                   // 最小延迟（毫秒）
  max: number;                   // 最大延迟（毫秒）
}
```

### 条件配置

```typescript
interface Condition {
  type: 'header' | 'query' | 'body' | 'path';  // 条件类型
  key: string;                   // 字段名
  operator: 'equals' | 'contains' | 'regex' | 'exists'; // 操作符
  value?: any;                   // 期望值
}
```

## 管理接口

### 健康检查

```
GET /_admin/health
```

响应：
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "memory": {
    "rss": 50331648,
    "heapTotal": 20971520,
    "heapUsed": 15728640,
    "external": 1048576
  }
}
```

### 服务器信息

```
GET /_admin/info
```

响应：
```json
{
  "name": "YC Mock Server",
  "version": "1.0.0",
  "node": "v18.17.0",
  "platform": "darwin",
  "arch": "x64",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 获取配置

```
GET /_admin/configs
```

响应：
```json
{
  "configs": [
    {
      "id": "users-api",
      "name": "Users API Mock",
      "enabled": true,
      "routes": [...]
    }
  ],
  "count": 1,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 重载配置

```
POST /_admin/reload
```

响应：
```json
{
  "message": "Mock configurations reloaded successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 获取日志

```
GET /_admin/logs?limit=100
```

响应：
```json
{
  "logs": [
    {
      "timestamp": "2024-01-01T00:00:00.000Z",
      "method": "GET",
      "path": "/api/users",
      "status": 200,
      "responseTime": 150,
      "mockId": "users-api",
      "routeId": "get-users"
    }
  ],
  "count": 1,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 清空日志

```
DELETE /_admin/logs
```

响应：
```json
{
  "message": "Logs cleared successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 模板变量

在响应体中可以使用以下模板变量：

### 请求参数

- `{{params.key}}` - 路径参数
- `{{query.key}}` - 查询参数  
- `{{body.key}}` - 请求体字段

### 系统变量

- `{{timestamp}}` - 当前ISO时间戳
- `{{random}}` - 随机数字符串

### 示例

```json
{
  "response": {
    "body": {
      "id": "{{params.id}}",
      "name": "{{body.name}}",
      "email": "{{body.email}}",
      "createdAt": "{{timestamp}}",
      "token": "{{random}}"
    }
  }
}
```

## 条件匹配

### 操作符说明

- `equals` - 精确匹配
- `contains` - 包含匹配（字符串）
- `regex` - 正则表达式匹配
- `exists` - 存在性检查

### 示例

```json
{
  "conditions": [
    {
      "type": "header",
      "key": "authorization",
      "operator": "exists"
    },
    {
      "type": "body",
      "key": "username",
      "operator": "equals",
      "value": "admin"
    },
    {
      "type": "query",
      "key": "type",
      "operator": "contains",
      "value": "user"
    }
  ]
}
```

## 错误处理

### 错误响应格式

```json
{
  "error": {
    "message": "错误描述",
    "code": "ERROR_CODE",
    "status": 500,
    "timestamp": "2024-01-01T00:00:00.000Z",
    "path": "/api/endpoint",
    "method": "GET"
  }
}
```

### 常见错误码

- `ROUTE_NOT_FOUND` - 路由未找到
- `INVALID_CREDENTIALS` - 认证失败
- `INTERNAL_ERROR` - 内部错误
