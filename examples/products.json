{"id": "products-api", "name": "Products API Mock", "description": "产品管理API的Mock配置示例", "enabled": true, "routes": [{"id": "get-products", "method": "GET", "path": "/api/products", "response": {"status": 200, "headers": {"Content-Type": "application/json", "X-Total-Count": "100"}, "body": {"data": [{"id": 1, "name": "iPhone 15", "price": 999.99, "category": "Electronics", "inStock": true, "description": "Latest iPhone model"}, {"id": 2, "name": "MacBook Pro", "price": 1999.99, "category": "Electronics", "inStock": false, "description": "Professional laptop"}], "pagination": {"page": "{{query.page}}", "limit": "{{query.limit}}", "total": 100}}}, "delay": {"min": 200, "max": 600}}, {"id": "search-products", "method": "GET", "path": "/api/products/search", "response": {"status": 200, "body": {"data": [{"id": 1, "name": "Search Result for: {{query.q}}", "price": 99.99, "category": "Electronics", "inStock": true}], "query": "{{query.q}}", "total": 1}}, "conditions": [{"type": "query", "key": "q", "operator": "exists"}]}, {"id": "get-product-by-id", "method": "GET", "path": "/api/products/:id", "response": {"status": 200, "body": {"data": {"id": "{{params.id}}", "name": "Product {{params.id}}", "price": 199.99, "category": "Electronics", "inStock": true, "description": "Description for product {{params.id}}", "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"], "specifications": {"weight": "1.5kg", "dimensions": "30x20x5cm", "warranty": "2 years"}}}}}, {"id": "create-product", "method": "POST", "path": "/api/products", "response": {"status": 201, "body": {"data": {"id": "{{random}}", "name": "{{body.name}}", "price": "{{body.price}}", "category": "{{body.category}}", "inStock": true, "createdAt": "{{timestamp}}"}, "message": "Product created successfully"}}, "delay": {"min": 500, "max": 1200}}, {"id": "update-product", "method": "PUT", "path": "/api/products/:id", "response": {"status": 200, "body": {"data": {"id": "{{params.id}}", "name": "{{body.name}}", "price": "{{body.price}}", "category": "{{body.category}}", "inStock": "{{body.inStock}}", "updatedAt": "{{timestamp}}"}, "message": "Product updated successfully"}}}, {"id": "delete-product", "method": "DELETE", "path": "/api/products/:id", "response": {"status": 204}, "delay": {"min": 300, "max": 700}}]}