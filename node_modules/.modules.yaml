hoistPattern:
  - '*'
hoistedDependencies:
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/express-serve-static-core@5.0.7':
    '@types/express-serve-static-core': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  accepts@2.0.0:
    accepts: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  arg@4.1.3:
    arg: private
  balanced-match@1.0.2:
    balanced-match: private
  basic-auth@2.0.1:
    basic-auth: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@2.2.0:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  create-require@1.1.1:
    create-require: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  depd@2.0.0:
    depd: private
  diff@4.0.2:
    diff: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  etag@1.8.1:
    etag: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@2.1.0:
    finalhandler: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  gopd@1.2.0:
    gopd: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-promise@4.0.0:
    is-promise: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@1.1.0:
    media-typer: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.0.0:
    ms: private
  negotiator@1.0.0:
    negotiator: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.1.0:
    on-headers: private
  once@1.4.0:
    once: private
  parseurl@1.3.3:
    parseurl: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  picomatch@2.3.1:
    picomatch: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pstree.remy@1.1.8:
    pstree.remy: private
  qs@6.14.0:
    qs: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  readdirp@3.6.0:
    readdirp: private
  require-directory@2.1.1:
    require-directory: private
  router@2.2.0:
    router: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.1.2:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  semver@7.7.2:
    semver: private
  send@1.2.0:
    send: private
  serve-static@2.2.0:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  statuses@2.0.2:
    statuses: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  supports-color@8.1.1:
    supports-color: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  touch@3.1.1:
    touch: private
  tree-kill@1.2.2:
    tree-kill: private
  tslib@2.8.1:
    tslib: private
  type-is@2.0.1:
    type-is: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@7.10.0:
    undici-types: private
  unpipe@1.0.0:
    unpipe: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  vary@1.1.2:
    vary: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Tue, 26 Aug 2025 03:10:52 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://mirrors.huaweicloud.com/repository/npm/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
