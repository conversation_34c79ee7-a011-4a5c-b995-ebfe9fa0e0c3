{"version": 3, "file": "Subscription.js", "sourceRoot": "", "sources": ["../../../src/internal/Subscription.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAA+C;AAC/C,kEAAiE;AAEjE,8CAA6C;AAY7C;IAwBE,sBAAoB,eAA4B;QAA5B,oBAAe,GAAf,eAAe,CAAa;QAdzC,WAAM,GAAG,KAAK,CAAC;QAEd,eAAU,GAAyC,IAAI,CAAC;QAMxD,gBAAW,GAA0C,IAAI,CAAC;IAMf,CAAC;IAOpD,kCAAW,GAAX;;QACE,IAAI,MAAyB,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YAGX,IAAA,UAAU,GAAK,IAAI,WAAT,CAAU;YAC5B,IAAI,UAAU,EAAE;gBACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;;wBAC7B,KAAqB,IAAA,eAAA,SAAA,UAAU,CAAA,sCAAA,8DAAE;4BAA5B,IAAM,QAAM,uBAAA;4BACf,QAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;yBACrB;;;;;;;;;iBACF;qBAAM;oBACL,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACzB;aACF;YAEO,IAAiB,gBAAgB,GAAK,IAAI,gBAAT,CAAU;YACnD,IAAI,uBAAU,CAAC,gBAAgB,CAAC,EAAE;gBAChC,IAAI;oBACF,gBAAgB,EAAE,CAAC;iBACpB;gBAAC,OAAO,CAAC,EAAE;oBACV,MAAM,GAAG,CAAC,YAAY,yCAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC5D;aACF;YAEO,IAAA,WAAW,GAAK,IAAI,YAAT,CAAU;YAC7B,IAAI,WAAW,EAAE;gBACf,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;;oBACxB,KAAwB,IAAA,gBAAA,SAAA,WAAW,CAAA,wCAAA,iEAAE;wBAAhC,IAAM,SAAS,wBAAA;wBAClB,IAAI;4BACF,aAAa,CAAC,SAAS,CAAC,CAAC;yBAC1B;wBAAC,OAAO,GAAG,EAAE;4BACZ,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAC;4BACtB,IAAI,GAAG,YAAY,yCAAmB,EAAE;gCACtC,MAAM,0CAAO,MAAM,WAAK,GAAG,CAAC,MAAM,EAAC,CAAC;6BACrC;iCAAM;gCACL,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;6BAClB;yBACF;qBACF;;;;;;;;;aACF;YAED,IAAI,MAAM,EAAE;gBACV,MAAM,IAAI,yCAAmB,CAAC,MAAM,CAAC,CAAC;aACvC;SACF;IACH,CAAC;IAoBD,0BAAG,GAAH,UAAI,QAAuB;;QAGzB,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE;YACjC,IAAI,IAAI,CAAC,MAAM,EAAE;gBAGf,aAAa,CAAC,QAAQ,CAAC,CAAC;aACzB;iBAAM;gBACL,IAAI,QAAQ,YAAY,YAAY,EAAE;oBAGpC,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;wBAChD,OAAO;qBACR;oBACD,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;iBAC3B;gBACD,CAAC,IAAI,CAAC,WAAW,GAAG,MAAA,IAAI,CAAC,WAAW,mCAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC5D;SACF;IACH,CAAC;IAOO,iCAAU,GAAlB,UAAmB,MAAoB;QAC7B,IAAA,UAAU,GAAK,IAAI,WAAT,CAAU;QAC5B,OAAO,UAAU,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7F,CAAC;IASO,iCAAU,GAAlB,UAAmB,MAAoB;QAC7B,IAAA,UAAU,GAAK,IAAI,WAAT,CAAU;QAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACnI,CAAC;IAMO,oCAAa,GAArB,UAAsB,MAAoB;QAChC,IAAA,UAAU,GAAK,IAAI,WAAT,CAAU;QAC5B,IAAI,UAAU,KAAK,MAAM,EAAE;YACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACpC,qBAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;SAC/B;IACH,CAAC;IAgBD,6BAAM,GAAN,UAAO,QAAsC;QACnC,IAAA,WAAW,GAAK,IAAI,YAAT,CAAU;QAC7B,WAAW,IAAI,qBAAS,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAEhD,IAAI,QAAQ,YAAY,YAAY,EAAE;YACpC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC9B;IACH,CAAC;IAjLa,kBAAK,GAAG,CAAC;QACrB,IAAM,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC;QACjC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,EAAE,CAAC;IA8KP,mBAAC;CAAA,AAnLD,IAmLC;AAnLY,oCAAY;AAqLZ,QAAA,kBAAkB,GAAG,YAAY,CAAC,KAAK,CAAC;AAErD,SAAgB,cAAc,CAAC,KAAU;IACvC,OAAO,CACL,KAAK,YAAY,YAAY;QAC7B,CAAC,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI,uBAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,uBAAU,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,uBAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CACnH,CAAC;AACJ,CAAC;AALD,wCAKC;AAED,SAAS,aAAa,CAAC,SAAwC;IAC7D,IAAI,uBAAU,CAAC,SAAS,CAAC,EAAE;QACzB,SAAS,EAAE,CAAC;KACb;SAAM;QACL,SAAS,CAAC,WAAW,EAAE,CAAC;KACzB;AACH,CAAC"}