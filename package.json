{"name": "yc-mock-server", "version": "1.0.0", "description": "A flexible mock server for API development and testing", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "start:dev": "concurrently \"pnpm build --watch\" \"nodemon dist/index.js\"", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "test": "echo \"Tests not configured yet\""}, "keywords": ["mock", "server", "api", "development", "testing", "express", "typescript"], "author": "xxb", "license": "MIT", "packageManager": "pnpm@10.14.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^24.3.0", "concurrently": "^9.2.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}