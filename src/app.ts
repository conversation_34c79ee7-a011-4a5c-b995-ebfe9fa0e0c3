import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { MockService } from './services/MockService';
import { MockHandler } from './middleware/mockHandler';
import { requestTimer, mockLogger } from './middleware/logger';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import { createAdminRoutes } from './routes/admin';
import config from './config';

export function createApp(): express.Application {
  const app = express();

  // Initialize services
  const mockService = new MockService();
  const mockHandler = new MockHandler(mockService);

  // Security middleware
  if (config.server.security.helmetEnabled) {
    app.use(helmet());
  }

  // CORS middleware
  app.use(cors({
    origin: config.server.cors.origin,
    methods: config.server.cors.methods,
    credentials: config.server.cors.credentials,
  }));

  // Request parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Logging middleware
  app.use(requestTimer);
  app.use(mockLogger(config.server.logging.format));

  // Admin routes (before mock handler)
  app.use('/_admin', createAdminRoutes(mockHandler));

  // Mock handler middleware (handles all other routes)
  app.use(mockHandler.handle);

  // Error handling middleware
  app.use(notFoundHandler);
  app.use(errorHandler);

  return app;
}

export default createApp;
