import dotenv from 'dotenv';
import { ServerConfig } from '../types';

// Load environment variables
dotenv.config();

export const serverConfig: ServerConfig = {
  port: parseInt(process.env.PORT || '3000', 10),
  host: process.env.HOST || 'localhost',
  cors: {
    origin: process.env.CORS_ORIGIN === '*' ? '*' : process.env.CORS_ORIGIN?.split(',') || ['*'],
    methods: process.env.CORS_METHODS || 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: process.env.CORS_CREDENTIALS === 'true',
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
  },
  security: {
    helmetEnabled: process.env.HELMET_ENABLED === 'true',
  },
};

export const mockConfig = {
  configPath: process.env.MOCK_CONFIG_PATH || './config/mocks',
  delayMin: parseInt(process.env.MOCK_DELAY_MIN || '0', 10),
  delayMax: parseInt(process.env.MOCK_DELAY_MAX || '1000', 10),
};

export default {
  server: serverConfig,
  mock: mockConfig,
};
