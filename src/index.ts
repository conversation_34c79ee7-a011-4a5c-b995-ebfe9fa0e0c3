import { createApp } from './app';
import config from './config';

function startServer(): void {
  const app = createApp();
  const { port, host } = config.server;

  const server = app.listen(port, host, () => {
    console.log(`🚀 YC Mock Server is running on http://${host}:${port}`);
    console.log(`📊 Admin panel available at http://${host}:${port}/_admin`);
    console.log(`📁 Mock configs path: ${config.mock.configPath}`);
    console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
      console.log('Server closed');
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
      console.log('Server closed');
      process.exit(0);
    });
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
  });
}

// Start the server
startServer();
