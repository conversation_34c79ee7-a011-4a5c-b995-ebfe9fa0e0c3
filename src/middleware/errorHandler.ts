import { Request, Response, NextFunction } from 'express';

export interface ApiError extends Error {
  status?: number;
  code?: string;
}

/**
 * Create API error
 */
export function createError(message: string, status: number = 500, code?: string): ApiError {
  const error = new Error(message) as ApiError;
  error.status = status;
  error.code = code;
  return error;
}

/**
 * 404 Not Found handler
 */
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = createError(`Route ${req.method} ${req.path} not found`, 404, 'ROUTE_NOT_FOUND');
  next(error);
}

/**
 * Global error handler
 */
export function errorHandler(
  error: ApiError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const status = error.status || 500;
  const message = error.message || 'Internal Server Error';
  const code = error.code || 'INTERNAL_ERROR';

  // Log error
  console.error(`Error ${status}: ${message}`, {
    method: req.method,
    path: req.path,
    stack: error.stack,
    code,
  });

  // Send error response
  res.status(status).json({
    error: {
      message,
      code,
      status,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method,
    },
  });
}

/**
 * Async error wrapper
 */
export function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
