import { Request, Response, NextFunction } from 'express';
import morgan from 'morgan';
import { LogEntry, MockRequest } from '../types';
import { formatResponseTime } from '../utils';

// Custom token for mock information
morgan.token('mock-info', (req: any) => {
  const mockReq = req as MockRequest;
  if (mockReq.mockId && mockReq.routeId) {
    return `[Mock: ${mockReq.mockId}/${mockReq.routeId}]`;
  }
  return '[No Mock]';
});

// Custom token for response time in ms
morgan.token('response-time-ms', (req: any, _res: Response) => {
  const mockReq = req as MockRequest;
  const startTime = mockReq.startTime;
  if (startTime) {
    return `${formatResponseTime(startTime)}ms`;
  }
  return '0ms';
});

// Custom format for mock server
const mockFormat = ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time-ms :mock-info';

/**
 * Request timing middleware
 */
export function requestTimer(req: Request, _res: Response, next: NextFunction): void {
  (req as MockRequest).startTime = process.hrtime();
  next();
}

/**
 * Mock logger middleware
 */
export function mockLogger(format: string = 'combined') {
  if (format === 'mock') {
    return morgan(mockFormat);
  }
  return morgan(format);
}

/**
 * Custom logger for structured logging
 */
export class Logger {
  private logs: LogEntry[] = [];
  private maxLogs: number = 1000;

  log(entry: LogEntry): void {
    this.logs.push(entry);
    
    // Keep only recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output
    const { timestamp, method, path, status, responseTime, mockId, routeId } = entry;
    const mockInfo = mockId && routeId ? `[${mockId}/${routeId}]` : '[No Mock]';
    
    console.log(
      `${timestamp.toISOString()} ${method} ${path} ${status} ${responseTime}ms ${mockInfo}`
    );
  }

  getLogs(limit?: number): LogEntry[] {
    if (limit) {
      return this.logs.slice(-limit);
    }
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }
}

export const logger = new Logger();
