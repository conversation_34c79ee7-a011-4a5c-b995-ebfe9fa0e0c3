import { Request, Response, NextFunction } from 'express';
import { MockService } from '../services/MockService';
import { MockRequest } from '../types';
import { logger } from './logger';
import { formatResponseTime } from '../utils';

export class MockHandler {
  constructor(private mockService: MockService) {}

  /**
   * Main mock handling middleware
   */
  handle = async (req: MockRequest, res: Response, next: NextFunction): Promise<void> => {
    const startTime = process.hrtime();
    
    try {
      // Find matching route
      const route = this.mockService.findMatchingRoute(req.method, req.path, req);
      
      if (!route) {
        // No mock route found, continue to next middleware
        return next();
      }

      // Set mock information on request
      req.mockId = route.id;
      req.routeId = route.id;

      // Process mock response
      const mockResponse = await this.mockService.processMockResponse(route, req);

      // Set headers
      if (mockResponse.headers) {
        Object.entries(mockResponse.headers).forEach(([key, value]) => {
          res.setHeader(key, value);
        });
      }

      // Set content type if not specified
      if (!res.getHeader('content-type')) {
        if (typeof mockResponse.body === 'object') {
          res.setHeader('content-type', 'application/json');
        } else if (typeof mockResponse.body === 'string') {
          res.setHeader('content-type', 'text/plain');
        }
      }

      // Send response
      res.status(mockResponse.status);
      
      if (mockResponse.body !== undefined) {
        if (typeof mockResponse.body === 'object') {
          res.json(mockResponse.body);
        } else {
          res.send(mockResponse.body);
        }
      } else {
        res.end();
      }

      // Log the request
      const responseTime = formatResponseTime(startTime);
      logger.log({
        timestamp: new Date(),
        method: req.method,
        path: req.path,
        status: mockResponse.status,
        responseTime,
        mockId: req.mockId,
        routeId: req.routeId,
      });

    } catch (error) {
      console.error('Error in mock handler:', error);
      next(error);
    }
  };

  /**
   * Middleware to reload mock configurations
   */
  reload = (req: Request, res: Response): void => {
    try {
      this.mockService.reload();
      res.json({
        message: 'Mock configurations reloaded successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to reload mock configurations',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  };

  /**
   * Get mock configurations
   */
  getConfigs = (req: Request, res: Response): void => {
    try {
      const configs = this.mockService.getMockConfigs();
      res.json({
        configs,
        count: configs.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to get mock configurations',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  };

  /**
   * Get logs
   */
  getLogs = (req: Request, res: Response): void => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : undefined;
      const logs = logger.getLogs(limit);
      res.json({
        logs,
        count: logs.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to get logs',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  };
}
