import { Router } from 'express';
import { <PERSON>ck<PERSON>and<PERSON> } from '../middleware/mockHandler';

export function createAdminRoutes(mockHandler: MockHandler): Router {
  const router = Router();

  // Health check
  router.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    });
  });

  // Reload mock configurations
  router.post('/reload', mockHandler.reload);

  // Get mock configurations
  router.get('/configs', mockHandler.getConfigs);

  // Get logs
  router.get('/logs', mockHandler.getLogs);

  // Clear logs
  router.delete('/logs', (req, res) => {
    try {
      const { logger } = require('../middleware/logger');
      logger.clearLogs();
      res.json({
        message: 'Logs cleared successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to clear logs',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Server info
  router.get('/info', (req, res) => {
    res.json({
      name: 'YC Mock Server',
      version: '1.0.0',
      node: process.version,
      platform: process.platform,
      arch: process.arch,
      timestamp: new Date().toISOString(),
    });
  });

  return router;
}
