import fs from 'fs';
import path from 'path';
import { MockConfig, MockRoute, MockRequest, MockResponse } from '../types';
import { loadJsonFile, getJsonFiles, matchesConditions, generateDelay, applyDelay } from '../utils';
import config from '../config';

export class MockService {
  private mockConfigs: Map<string, MockConfig> = new Map();
  private routes: Map<string, MockRoute[]> = new Map();

  constructor() {
    this.loadMockConfigs();
  }

  /**
   * Load all mock configurations from files
   */
  public loadMockConfigs(): void {
    console.log(`Loading mock configurations from: ${config.mock.configPath}`);
    
    const configFiles = getJsonFiles(config.mock.configPath);
    this.mockConfigs.clear();
    this.routes.clear();

    configFiles.forEach(filePath => {
      const mockConfig = loadJsonFile<MockConfig>(filePath);
      if (mockConfig && mockConfig.enabled) {
        this.mockConfigs.set(mockConfig.id, mockConfig);
        this.registerRoutes(mockConfig);
        console.log(`Loaded mock config: ${mockConfig.name} (${mockConfig.routes.length} routes)`);
      }
    });

    console.log(`Total loaded configs: ${this.mockConfigs.size}`);
  }

  /**
   * Register routes for a mock configuration
   */
  private registerRoutes(config: MockConfig): void {
    config.routes.forEach(route => {
      const key = `${route.method}:${route.path}`;
      if (!this.routes.has(key)) {
        this.routes.set(key, []);
      }
      this.routes.get(key)!.push(route);
    });
  }

  /**
   * Find matching route for request
   */
  public findMatchingRoute(method: string, path: string, req: MockRequest): MockRoute | null {
    const key = `${method.toUpperCase()}:${path}`;
    const routes = this.routes.get(key);
    
    if (!routes) {
      // Try to find routes with path parameters
      for (const [routeKey, routeList] of this.routes.entries()) {
        const [routeMethod, routePath] = routeKey.split(':');
        if (routeMethod === method.toUpperCase() && this.matchesPath(routePath, path)) {
          const matchingRoutes = routeList.filter(route => 
            !route.conditions || matchesConditions(req, route.conditions)
          );
          if (matchingRoutes.length > 0) {
            return matchingRoutes[0];
          }
        }
      }
      return null;
    }

    // Find route that matches conditions
    const matchingRoute = routes.find(route => 
      !route.conditions || matchesConditions(req, route.conditions)
    );

    return matchingRoute || routes[0]; // Return first route if no conditions match
  }

  /**
   * Check if route path matches request path (with parameters)
   */
  private matchesPath(routePath: string, requestPath: string): boolean {
    const routeSegments = routePath.split('/');
    const requestSegments = requestPath.split('/');

    if (routeSegments.length !== requestSegments.length) {
      return false;
    }

    return routeSegments.every((segment, index) => {
      if (segment.startsWith(':')) {
        return true; // Parameter segment matches anything
      }
      return segment === requestSegments[index];
    });
  }

  /**
   * Process mock response
   */
  public async processMockResponse(route: MockRoute, req: MockRequest): Promise<MockResponse> {
    // Apply delay if configured
    if (route.delay) {
      const delay = generateDelay(route.delay);
      await applyDelay(delay);
    }

    // Clone response to avoid modifying original
    const response = { ...route.response };

    // Process response body templates
    if (response.body !== undefined) {
      response.body = this.processTemplateInObject(response.body, req);
    }

    // Load response from file if specified
    if (response.file) {
      try {
        const filePath = path.resolve(config.mock.configPath, response.file);
        if (fs.existsSync(filePath)) {
          const fileContent = fs.readFileSync(filePath, 'utf-8');
          if (response.file.endsWith('.json')) {
            response.body = JSON.parse(fileContent);
          } else {
            response.body = fileContent;
          }
        }
      } catch (error) {
        console.error(`Error loading response file ${response.file}:`, error);
      }
    }

    return response;
  }

  /**
   * Process templates in any object (recursive)
   */
  private processTemplateInObject(obj: any, req: MockRequest): any {
    if (typeof obj === 'string') {
      return this.processTemplate(obj, req);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.processTemplateInObject(item, req));
    }

    if (obj && typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = this.processTemplateInObject(value, req);
      }
      return result;
    }

    return obj;
  }

  /**
   * Process template strings in response body
   */
  private processTemplate(template: string, req: MockRequest): string {
    return template.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
      try {
        // Simple template processing - can be extended
        const trimmed = expression.trim();
        
        if (trimmed.startsWith('query.')) {
          const key = trimmed.substring(6);
          return req.query[key] || '';
        }
        
        if (trimmed.startsWith('body.')) {
          const key = trimmed.substring(5);
          return req.body?.[key] || '';
        }
        
        if (trimmed.startsWith('params.')) {
          const key = trimmed.substring(7);
          return req.params[key] || '';
        }
        
        if (trimmed === 'timestamp') {
          return new Date().toISOString();
        }
        
        if (trimmed === 'random') {
          return Math.random().toString();
        }
        
        return match; // Return original if no match
      } catch (error) {
        console.error(`Error processing template expression ${expression}:`, error);
        return match;
      }
    });
  }

  /**
   * Get all mock configurations
   */
  public getMockConfigs(): MockConfig[] {
    return Array.from(this.mockConfigs.values());
  }

  /**
   * Get mock configuration by ID
   */
  public getMockConfig(id: string): MockConfig | undefined {
    return this.mockConfigs.get(id);
  }

  /**
   * Reload configurations
   */
  public reload(): void {
    this.loadMockConfigs();
  }
}
