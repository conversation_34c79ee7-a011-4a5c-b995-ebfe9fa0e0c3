export interface MockConfig {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  routes: MockRoute[];
}

export interface MockRoute {
  id: string;
  method: HttpMethod;
  path: string;
  response: MockResponse;
  delay?: DelayConfig;
  conditions?: Condition[];
}

export interface MockResponse {
  status: number;
  headers?: Record<string, string>;
  body?: any;
  file?: string;
}

export interface DelayConfig {
  min: number;
  max: number;
}

export interface Condition {
  type: 'header' | 'query' | 'body' | 'path';
  key: string;
  operator: 'equals' | 'contains' | 'regex' | 'exists';
  value?: any;
}

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

export interface ServerConfig {
  port: number;
  host: string;
  cors: {
    origin: string | string[];
    methods: string;
    credentials: boolean;
  };
  logging: {
    level: string;
    format: string;
  };
  security: {
    helmetEnabled: boolean;
  };
}

import { Request, Response } from 'express';

export interface MockRequest extends Request {
  mockId?: string;
  routeId?: string;
  startTime?: [number, number];
}

export interface MockContext {
  request: MockRequest;
  response: Response;
  config: MockRoute;
  timestamp: Date;
}

export interface LogEntry {
  timestamp: Date;
  method: string;
  path: string;
  status: number;
  responseTime: number;
  mockId?: string;
  routeId?: string;
}
