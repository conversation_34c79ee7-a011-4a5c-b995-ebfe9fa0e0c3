import fs from 'fs';
import path from 'path';
import { DelayConfig, Condition, MockRequest } from '../types';

/**
 * Generate random delay within specified range
 */
export function generateDelay(config: DelayConfig): number {
  const { min, max } = config;
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Apply delay to response
 */
export function applyDelay(delay: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, delay));
}

/**
 * Check if request matches conditions
 */
export function matchesConditions(req: MockRequest, conditions: Condition[]): boolean {
  return conditions.every(condition => matchesCondition(req, condition));
}

/**
 * Check if request matches a single condition
 */
export function matchesCondition(req: MockRequest, condition: Condition): boolean {
  const { type, key, operator, value } = condition;
  
  let actualValue: any;
  
  switch (type) {
    case 'header':
      actualValue = req.headers[key.toLowerCase()];
      break;
    case 'query':
      actualValue = req.query[key];
      break;
    case 'body':
      actualValue = req.body?.[key];
      break;
    case 'path':
      actualValue = req.params[key];
      break;
    default:
      return false;
  }
  
  switch (operator) {
    case 'exists':
      return actualValue !== undefined && actualValue !== null;
    case 'equals':
      return actualValue === value;
    case 'contains':
      return typeof actualValue === 'string' && actualValue.includes(value);
    case 'regex':
      return typeof actualValue === 'string' && new RegExp(value).test(actualValue);
    default:
      return false;
  }
}

/**
 * Load JSON file safely
 */
export function loadJsonFile<T>(filePath: string): T | null {
  try {
    const fullPath = path.resolve(filePath);
    if (!fs.existsSync(fullPath)) {
      return null;
    }
    const content = fs.readFileSync(fullPath, 'utf-8');
    return JSON.parse(content) as T;
  } catch (error) {
    console.error(`Error loading JSON file ${filePath}:`, error);
    return null;
  }
}

/**
 * Save JSON file safely
 */
export function saveJsonFile<T>(filePath: string, data: T): boolean {
  try {
    const fullPath = path.resolve(filePath);
    const dir = path.dirname(fullPath);
    
    // Ensure directory exists
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(fullPath, JSON.stringify(data, null, 2), 'utf-8');
    return true;
  } catch (error) {
    console.error(`Error saving JSON file ${filePath}:`, error);
    return false;
  }
}

/**
 * Get all JSON files in directory
 */
export function getJsonFiles(dirPath: string): string[] {
  try {
    const fullPath = path.resolve(dirPath);
    if (!fs.existsSync(fullPath)) {
      return [];
    }
    
    return fs.readdirSync(fullPath)
      .filter(file => file.endsWith('.json'))
      .map(file => path.join(fullPath, file));
  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error);
    return [];
  }
}

/**
 * Generate unique ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * Format response time
 */
export function formatResponseTime(startTime: [number, number]): number {
  const diff = process.hrtime(startTime);
  return Math.round((diff[0] * 1e3) + (diff[1] * 1e-6));
}
