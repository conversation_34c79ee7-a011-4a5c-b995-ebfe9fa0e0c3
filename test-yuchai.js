const http = require('http');

// 测试玉柴API接口
function testYuchaiAPI() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/yuchai_api/vue_report/zhenlai_vue_report_caption_worksho',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const jsonData = JSON.parse(data);
        console.log('\n=== 玉柴报表API响应 ===');
        console.log(`代码: ${jsonData.code}`);
        console.log(`消息: ${jsonData.message}`);
        console.log(`数据总数: ${jsonData.data.total}`);
        console.log(`结果数量: ${jsonData.data.results.length}`);
        
        console.log('\n=== 前3条记录 ===');
        jsonData.data.results.slice(0, 3).forEach((item, index) => {
          console.log(`${index + 1}. ${item.manufacturing_order_number} - ${item.figure_number_name}`);
          console.log(`   计划数量: ${item.planned_production_quantity}, 完成率: ${item.completion_rate_percent}`);
          console.log(`   质量状态: ${item.quality_status_identification || '未设置'}`);
        });
        
        console.log('\n✅ 接口测试成功！');
      } catch (error) {
        console.error('解析JSON失败:', error);
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求失败:', error);
  });

  req.end();
}

console.log('🚀 开始测试玉柴API接口...\n');
testYuchaiAPI();
